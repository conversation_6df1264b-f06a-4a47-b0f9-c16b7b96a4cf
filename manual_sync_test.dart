import 'dart:convert';
import 'dart:math';
import 'package:dio/dio.dart';

/// Manual comprehensive sync test for Odoo integration
/// This script tests all sync functionality without Flutter test restrictions
void main() async {
  print('🚀 Starting Comprehensive Odoo Sync Test');
  print('=' * 60);
  
  final tester = OdooSyncTester();
  
  try {
    // Test 1: Basic Connection
    await tester.testConnection();
    
    // Test 2: Fetch data from Odoo (currencies, companies, partners, products)
    await tester.testFetchFromOdoo();
    
    // Test 3: Create test data locally and sync to Odoo
    await tester.testSyncToOdoo();
    
    // Test 4: Verify data exists in Odoo
    await tester.testVerifyInOdoo();
    
    print('\n' + '=' * 60);
    print('✅ All sync tests completed successfully!');
    
  } catch (e) {
    print('\n' + '=' * 60);
    print('❌ Sync test failed: $e');
  }
}

class OdooSyncTester {
  static const String odooUrl = 'https://erp.kanjan.co.zw';
  static const String database = 'piggypro';
  static const String username = '<EMAIL>';
  static const String password = 'Secret1234';
  
  late final Dio dio;
  late int userId;
  
  // Store created record IDs for cleanup
  final List<int> createdPartnerIds = [];
  final List<int> createdProductIds = [];
  final List<int> createdInvoiceIds = [];
  
  OdooSyncTester() {
    dio = Dio();
  }
  
  /// Test basic Odoo connection and authentication
  Future<void> testConnection() async {
    print('\n🔗 Testing Odoo Connection...');
    
    final authData = {
      "jsonrpc": "2.0",
      "method": "call",
      "params": {
        "service": "common",
        "method": "authenticate",
        "args": [database, username, password, {}]
      }
    };
    
    final response = await dio.post('$odooUrl/jsonrpc', data: authData);
    
    if (response.data['result'] != null) {
      userId = response.data['result'];
      print('✅ Authentication successful! User ID: $userId');
    } else {
      throw Exception('Authentication failed: ${response.data}');
    }
  }
  
  /// Test fetching data from Odoo (simulating sync FROM Odoo)
  Future<void> testFetchFromOdoo() async {
    print('\n📥 Testing Fetch FROM Odoo...');
    
    // Test fetching currencies
    await _testFetchCurrencies();
    
    // Test fetching companies
    await _testFetchCompanies();
    
    // Test fetching partners (clients)
    await _testFetchPartners();
    
    // Test fetching products
    await _testFetchProducts();
    
    print('✅ Fetch from Odoo tests completed');
  }
  
  Future<void> _testFetchCurrencies() async {
    print('  💱 Fetching currencies...');
    
    final result = await _odooCall('res.currency', 'search_read', [
      [], // No domain filter
      ['name', 'symbol', 'rate']
    ]);
    
    print('    Found ${result.length} currencies');
    for (var currency in result.take(3)) {
      print('    - ${currency['name']} (${currency['symbol']})');
    }
  }
  
  Future<void> _testFetchCompanies() async {
    print('  🏢 Fetching companies...');
    
    final result = await _odooCall('res.company', 'search_read', [
      [], // No domain filter
      ['name', 'email', 'phone']
    ]);
    
    print('    Found ${result.length} companies');
    for (var company in result.take(3)) {
      print('    - ${company['name']}');
    }
  }
  
  Future<void> _testFetchPartners() async {
    print('  👥 Fetching partners (clients)...');
    
    final result = await _odooCall('res.partner', 'search_read', [
      [['customer_rank', '>', 0]], // Only customers
      ['name', 'email', 'phone']
    ], {'limit': 5});
    
    print('    Found ${result.length} customers');
    for (var partner in result) {
      print('    - ${partner['name']} (${partner['email'] ?? 'No email'})');
    }
  }
  
  Future<void> _testFetchProducts() async {
    print('  📦 Fetching products...');
    
    final result = await _odooCall('product.product', 'search_read', [
      [], // No domain filter
      ['name', 'list_price', 'default_code']
    ], {'limit': 5});
    
    print('    Found ${result.length} products');
    for (var product in result) {
      print('    - ${product['name']} (\$${product['list_price']})');
    }
  }
  
  /// Test creating data locally and syncing TO Odoo
  Future<void> testSyncToOdoo() async {
    print('\n📤 Testing Sync TO Odoo...');
    
    // Create test partner (client)
    await _testCreatePartner();
    
    // Create test product
    await _testCreateProduct();
    
    // Create test invoice
    await _testCreateInvoice();
    
    print('✅ Sync to Odoo tests completed');
  }
  
  Future<void> _testCreatePartner() async {
    print('  👤 Creating test partner...');
    
    final random = Random();
    final partnerData = {
      'name': 'Test Client ${random.nextInt(1000)}',
      'email': 'test${random.nextInt(1000)}@example.com',
      'phone': '******-${random.nextInt(9000) + 1000}',
      'customer_rank': 1,
      'is_company': false,
    };
    
    final partnerId = await _odooCall('res.partner', 'create', [partnerData]);
    createdPartnerIds.add(partnerId);
    
    print('    ✅ Created partner with ID: $partnerId');
  }
  
  Future<void> _testCreateProduct() async {
    print('  📦 Creating test product...');
    
    final random = Random();
    final productData = {
      'name': 'Test Product ${random.nextInt(1000)}',
      'list_price': (random.nextDouble() * 100 + 10).toStringAsFixed(2),
      'default_code': 'TEST${random.nextInt(1000)}',
      'type': 'consu', // Consumable product
    };
    
    final productId = await _odooCall('product.product', 'create', [productData]);
    createdProductIds.add(productId);
    
    print('    ✅ Created product with ID: $productId');
  }
  
  Future<void> _testCreateInvoice() async {
    print('  🧾 Creating test invoice...');
    
    if (createdPartnerIds.isEmpty) {
      print('    ⚠️ No partner created, skipping invoice creation');
      return;
    }
    
    final random = Random();
    final invoiceData = {
      'partner_id': createdPartnerIds.first,
      'move_type': 'out_invoice',
      'state': 'draft', // Important: invoices should be in draft state
      'invoice_line_ids': [
        [0, 0, {
          'name': 'Test Service ${random.nextInt(100)}',
          'quantity': 1,
          'price_unit': (random.nextDouble() * 100 + 50).toStringAsFixed(2),
        }]
      ],
    };
    
    final invoiceId = await _odooCall('account.move', 'create', [invoiceData]);
    createdInvoiceIds.add(invoiceId);
    
    print('    ✅ Created invoice with ID: $invoiceId (in draft state)');
  }
  
  /// Test verifying that created data exists in Odoo
  Future<void> testVerifyInOdoo() async {
    print('\n🔍 Verifying created data in Odoo...');
    
    // Verify partners
    if (createdPartnerIds.isNotEmpty) {
      final partners = await _odooCall('res.partner', 'read', [
        createdPartnerIds,
        ['name', 'email']
      ]);
      print('  ✅ Verified ${partners.length} partners in Odoo');
    }
    
    // Verify products
    if (createdProductIds.isNotEmpty) {
      final products = await _odooCall('product.product', 'read', [
        createdProductIds,
        ['name', 'list_price']
      ]);
      print('  ✅ Verified ${products.length} products in Odoo');
    }
    
    // Verify invoices
    if (createdInvoiceIds.isNotEmpty) {
      final invoices = await _odooCall('account.move', 'read', [
        createdInvoiceIds,
        ['name', 'state', 'amount_total']
      ]);
      print('  ✅ Verified ${invoices.length} invoices in Odoo');
      
      for (var invoice in invoices) {
        print('    - Invoice ${invoice['name']}: ${invoice['state']} (\$${invoice['amount_total']})');
      }
    }
    
    print('✅ All created data verified in Odoo');
  }
  
  /// Helper method to make Odoo API calls
  Future<dynamic> _odooCall(String model, String method, List args, [Map<String, dynamic>? kwargs]) async {
    final data = {
      "jsonrpc": "2.0",
      "method": "call",
      "params": {
        "service": "object",
        "method": "execute_kw",
        "args": [database, userId, password, model, method, args, kwargs ?? {}]
      }
    };
    
    final response = await dio.post('$odooUrl/jsonrpc', data: data);
    
    if (response.data['error'] != null) {
      throw Exception('Odoo API Error: ${response.data['error']}');
    }
    
    return response.data['result'];
  }
}
