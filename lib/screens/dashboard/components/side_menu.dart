import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/screens/profile/edit/profile_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:invoicer/core/types/conflict.dart';
import 'package:invoicer/screens/login/login_screen.dart';
import 'package:invoicer/screens/receipt/receipts_home_screen.dart';

import '../../../core/constants/constants.dart';
import '../../../core/db/drift/database.dart';
import '../../../core/repositories/drift/repository_provider_riverpod.dart';
import '../../../core/providers/sync/sync_provider.dart';
import '../../../core/utils/user_preference.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/utils/shared_pref_service.dart';
import '../../client/clients_home_screen.dart';
// import '../../expense/expenses_home_screen.dart'; // Not needed as expenses are hidden
import '../../invoice/invoices_home_screen.dart';
// import '../../order/orders_home_screen.dart';
import '../../profile/profiles_home_screen.dart';
// import '../../quote/quotes_home_screen.dart';
import '../home_screen.dart';
import 'conflict_selection_section.dart';


var userId;
class SideMenu extends ConsumerWidget {
  const SideMenu({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sharedPref = ref.watch(sharedPreferencesServiceProvider);
    final syncProvider = ref.watch(syncNotifierProvider);

    String? logoPath;



    Future<void> setUserId() async{
      SharedPreferences prefs = await SharedPreferences.getInstance();
      userId = (await prefs.getString(UserPreference.userId));

      try {
        // Get active business ID
        int? activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

        if (activeBusiness != null) {
          if (kIsWeb) {
            // Get business details
            ResCompanyTableData? biz = await ref.read(resCompanyRepositoryProvider).getById(activeBusiness);
            if (biz?.logo != null) logoPath = biz?.logo;
          } else {
            logoPath = await getLogoPath(activeBusiness);
          }
        }
      } catch (e,st) {
        print('$e \n$st');
        print("Error getting active business: $e");
      }
    }


    Future<String> getName() async{
      var prefs = await SharedPreferences.getInstance();
      var file =  (await prefs.getString(UserPreference.activeBusinessName));
      if(file==null)return "";
      return file;
    }

    logoutRoute() async {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) =>
                Login(title: "You logged out.")),
      );
    }

    logoutDialog(){
      // Create a StatefulBuilder to manage loading state within the dialog
      return showDialog(
          context: context,
          builder: (_) {
            bool isLoggingOut = false;

            return StatefulBuilder(
              builder: (context, setState) {
                return AlertDialog(
                    title: Center(
                      child: Text("Confirm Logout"),
                    ),
                    content: SingleChildScrollView(
                      child: Column(
                        children: [
                          Text(
                              "Logging out will save your data online and erase"
                                  " local records. You need an internet connection. Sign in again "
                                  "to view your records. Are you sure want to logout?"),
                          SizedBox(
                            height: 16,
                          ),
                          if (isLoggingOut)
                            Column(
                              children: [
                                CircularProgressIndicator(),
                                SizedBox(height: 10),
                                Text("Logging out...", style: TextStyle(fontStyle: FontStyle.italic)),
                                SizedBox(height: 10),
                              ],
                            ),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(width: 20,),
                                ElevatedButton.icon(
                                    icon: Icon(
                                      Icons.logout,
                                      size: 14,
                                    ),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red),
                                    onPressed: isLoggingOut ? null : () async {
                                      // Set loading state to true
                                      setState(() {
                                        isLoggingOut = true;
                                      });

                                      SystemChannels.textInput.invokeMethod('TextInput.clearClientFeatures');
                                      SharedPreferences prefs = await SharedPreferences.getInstance();

                                      try {
                                        // Sync data before logging out
                                        var res = await ref
                                            .read(syncNotifierProvider.notifier)
                                            .syncAll(false);

                                        if (res == true || (kIsWeb || strictWeb)) {
                                          // Clear preferences and credentials
                                          // Note: Database clearing is handled by the SharedPrefService

                                          // Reset user credentials
                                          await sharedPref.resetUserCredentials();

                                          // Reset preferences
                                          prefs.setBool(UserPreference.skip, false);

                                          // Close dialog
                                          context.pop();

                                          // Navigate to login screen
                                          SchedulerBinding.instance.addPostFrameCallback((_) {
                                            logoutRoute();
                                          });
                                        } else {
                                          // Reset loading state
                                          setState(() {
                                            isLoggingOut = false;
                                          });

                                          // Close dialog
                                          context.pop();

                                          // Show error message
                                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                            content: Text("Log out failed. Make sure you are connected to the internet."),
                                          ));
                                        }
                                      } catch (e,st) {
                                        print('$e \n$st');
                                        // Reset loading state
                                        setState(() {
                                          isLoggingOut = false;
                                        });

                                        // Close dialog
                                        context.pop();

                                        // Show error message
                                        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                          content: Text("Error during logout: ${e.toString()}"),
                                        ));
                                      }
                                    },
                                    label: Text("Logout")),
                                SizedBox(
                                  width: 20,
                                ),
                                ElevatedButton.icon(
                                    icon: Icon(
                                      Icons.close,
                                      size: 14,
                                    ),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.grey),
                                    onPressed: isLoggingOut ? null : () {
                                      context.pop();
                                    },
                                    label: Text("Cancel")),

                              ],
                            ),
                          )
                    ],
                  ),
                ));
              }
            );
          });
    }



    return Drawer(
      child: SingleChildScrollView(
        // it enables scrolling
        child: Column(
          children: [
            DrawerHeader(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: defaultPadding * 2,
                    ),
                    FutureBuilder(
                      future:  setUserId(),
                      builder: (context, snapshot) {
                        print(snapshot);
                        return  Column(
                          children: [


                            // if online & logo use netwrk
                            if((kIsWeb) && logoPath!=null )
                              SizedBox(
                                height: 50,
                                child: Image.network(logoPath!)

                            ),

                            // if offline & path use path
                            if(!(kIsWeb) && logoPath!=null )
                              SizedBox(
                                height: 50,
                                child:  Image.file(File(logoPath!), scale:1),
                              ),

                            // else use placeholder
                            if( logoPath==null )
                              SizedBox(
                                height: 50,
                                child:   Image.asset("assets/logo/logo_icon.png", scale:1) ,
                              ),
                          ],
                        );
                      },
                    ),
                    SizedBox(
                      height: defaultPadding/2,
                    ),
                    FutureBuilder(
                      future:  getName(),
                      builder: (context, snapshot) {
                        print(snapshot);
                        return  snapshot.data==""? Text("Invoicer"):Text(snapshot.data.toString(), overflow: TextOverflow.clip,);
                      },
                    ),

                  ],
                )),



            Builder(
              builder: (context) {
                // Check the state using the pattern matching methods
                if (syncProvider.isLoading) {
                  return Center(child: CircularProgressIndicator());
                } else {
                  // Handle data state
                  if (syncProvider.toString().contains('_SyncStateData')) {
                    // Extract data using reflection since we can't directly access private types
                    final syncProviderStr = syncProvider.toString();
                    print(syncProviderStr);
                    print("Conflict detected.");

                    // Create conflict object
                    Conflict conflict = new Conflict();
                    conflict.object = syncProvider;
                    conflict.objectType = "invoice";

                    SchedulerBinding.instance.addPostFrameCallback((_) {
                      showDialog(
                        context: context,
                        builder: (_) {
                          return AlertDialog(
                            title: Center(
                              child: Text("Resolve Conflicts"),
                            ),
                            content: SingleChildScrollView(
                              child: Column(
                                children: [
                                  Text(
                                    "The following conflicts were encountered while syncing data. Select to resolve.",
                                    style: const TextStyle(color: Colors.greenAccent, fontSize: 14),
                                  ),
                                  SizedBox(height: 16),
                                  SizedBox(
                                    width: 1000,
                                    height: 900,
                                    child: SingleChildScrollView(
                                      child: ConflictSelectionSection(conflict: conflict)
                                    ),
                                  ),
                                ],
                              ),
                            )
                          );
                        }
                      );
                    });
                    return syncWidget(error: false, ref: ref);
                  }

                  // Handle error state
                  if (syncProvider.isError) {
                    log("Sync error occurred");
                    return syncWidget(error: true, ref: ref);
                  }

                  // Handle other states (initial, loaded)
                  return syncWidget(error: false, ref: ref);
                }
              },
            ),


            DrawerListTile(
              title: "Dashboard",
              svgSrc: "assets/icons/menu_dashbord.svg",
              press: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => HomeScreen(source: 'side menu',)),
                );
              },
            ),
            DrawerListTile(
              title: "Invoices",
              svgSrc: "assets/icons/menu_tran.svg",
              press: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => RegisterHomeScreen()),
                );
              },
            ),
            // DrawerListTile(
            //   title: "Printer",
            //   svgSrc: "assets/icons/menu_tran.svg",
            //   press: () {
            //     Navigator.push(
            //       context,
            //       MaterialPageRoute(builder: (context) => PrintingWidget()),
            //     );
            //   },
            // ),
            // DrawerListTile(
            //   title: "Quotations",
            //   svgSrc: "assets/icons/menu_profile.svg",
            //   press: () {
            //     Navigator.pushReplacement(
            //       context,
            //       MaterialPageRoute(builder: (context) => QuotesHomeScreen()),
            //     );
            //   },
            // ),
            // DrawerListTile(
            //   title: "Receipts",
            //   svgSrc: "assets/icons/menu_notification.svg",
            //   press: () {
            //     Navigator.pushReplacement(
            //       context,
            //       MaterialPageRoute(builder: (context) => ReceiptsHomeScreen()),
            //     );
            //   },
            // ),
            // DrawerListTile(
            //   title: "Orders",
            //   svgSrc: "assets/icons/menu_task.svg",
            //   press: () {
            //     Navigator.pushReplacement(
            //       context,
            //       MaterialPageRoute(builder: (context) => OrdersHomeScreen()),
            //     );
            //   },
            // ),
            // DrawerListTile(
            //   title: "Products",
            //   svgSrc: "assets/icons/menu_store.svg",
            //   press: () {
            //     Navigator.pushReplacement(
            //       context,
            //       MaterialPageRoute(builder: (context) => ProductsHomeScreen()),
            //     );
            //   },
            // ),
            // DrawerListTile(
            //   title: "Subscriptions",
            //   svgSrc: "assets/icons/menu_task.svg",
            //   press: () {
            //     Navigator.pushReplacement(
            //       context,
            //       MaterialPageRoute(builder: (context) => MemberSubscriptionsHomeScreen()),
            //     );
            //   },
            // ),
            // Expenses option hidden
            // DrawerListTile(
            //   title: "Expenses",
            //   svgSrc: "assets/icons/menu_task.svg",
            //   press: () => Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => new ExpensesHomeScreen()),),
            // ),
            DrawerListTile(
              title: "Clients",
              // title: "Clients & Vendors",
              svgSrc: "assets/icons/menu_task.svg",
              press: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => new ClientsHomeScreen()),
                );
              },
            ),
            DrawerListTile(
              title: "Settings",
              svgSrc: "assets/icons/menu_setting.svg",
              press: () async {
                SharedPreferences prefs = await SharedPreferences.getInstance();
                prefs.setBool(UserPreference.skip,
                    false);
                // SchedulerBinding.instance.addPostFrameCallback((_) {//               context.pop());
                //
                // });
                SchedulerBinding.instance.addPostFrameCallback((_) {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => ProfileHomeScreen()),
                  );
                });

              },
            ),
            DrawerListTile(
              title: "Logout",
              svgSrc: "assets/icons/menu_profile.svg",
              press: () async {
                SharedPreferences prefs = await SharedPreferences.getInstance();
                var userId = (await prefs.getString(UserPreference.userId));
                if(userId==null){
                  prefs.setBool(UserPreference.skip, false);

                  // context.pop();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            Login(title: "You logged out.")),
                  );
                }else {
                  logoutDialog();
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}

class DrawerListTile extends StatelessWidget {
  const DrawerListTile({
    Key? key,
    // For selecting those three line once press "Command+D"
    required this.title,
    required this.svgSrc,
    required this.press,
  }) : super(key: key);

  final String title, svgSrc;
  final VoidCallback press;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: press,
      horizontalTitleGap: 0.0,
      leading: SvgPicture.asset(
        svgSrc,
        // color: Colors.white54,
        height: 16,
        colorFilter: ColorFilter.mode( Theme.of(context).colorScheme.outlineVariant, BlendMode.srcIn),
        // color:  Theme.of(context).colorScheme.outline,
      ),
      title: Text(
        title,
        // style: TextStyle(color: Colors.white54),
      ),
    );
  }
}


class syncWidget extends StatefulWidget {
  @override
  syncWidgetState createState() => syncWidgetState(this.ref);
  syncWidget({
    required this.error,
    required this.ref
  });

  final bool error;
  final WidgetRef ref;


}

// class _FormMaterialBackupState extends State<FormMaterialBackup> {

class syncWidgetState extends State<syncWidget> {
  syncWidgetState(this.ref);

  var userId;
  var lastSyncDate;
  SharedPreferences? prefs;
  bool isSyncing = false; // Add loading state

  getUserId() async{
    prefs = await SharedPreferences.getInstance();
    userId = (await prefs!.getString(UserPreference.userId));
    lastSyncDate = (await prefs!.getString(UserPreference.lastSyncDate));
    setState(() {});
  }

  getUserId2() async{
    prefs = await SharedPreferences.getInstance();
    userId = (await prefs!.getString(UserPreference.userId));
    lastSyncDate = (await prefs!.getString(UserPreference.lastSyncDate));
  }

  @override
  void initState() {
    super.initState();
    getUserId();
  }

  final WidgetRef ref;

  @override
  Widget build(BuildContext context) {
    getUserId2();

    return !(kIsWeb||strictWeb) ? SizedBox(
      width: 150,
      child: Column(
        children: [
          isSyncing
              ? Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 8),
                    Text("Syncing...", style: TextStyle(fontSize: 12)),
                  ],
                )
              : userId!=null ?TextButton(
                  child: Text(widget.error?"sync err" : "sync", style: TextStyle(color: widget.error? Colors.white :Colors.black),),
                  style: TextButton.styleFrom(
                    backgroundColor: widget.error? Colors.black :Colors.white,
                    padding: EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical:
                      defaultPadding / (Responsive.isMobile(context) ? 3 : 2),
                    ),
                  ),
                  onPressed: () async {
                    setState(() { isSyncing = true; });
                    await ref.read(syncNotifierProvider.notifier).syncAll(true);
                    setState(() { isSyncing = false; });
                  },
                )
              : TextButton(
                  child: Text("Sign in to sync"),
                  style: TextButton.styleFrom(
                    backgroundColor:  Colors.grey,
                    padding: EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical:
                      defaultPadding / (Responsive.isMobile(context) ? 3 : 2),
                    ),
                  ),
                  onPressed: () async {
                    SharedPreferences prefs = await SharedPreferences.getInstance();
                    prefs.setBool(UserPreference.skip, false);
                    Navigator.of(context).pop();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) =>
                              Login(title: "You logged out.")),
                    );
                  },
                ),
           widget.error?
            Tooltip(
              message: "Make sure you have a good internet connection for sync.",
              child: Text("Make sure you have a good internet connection for sync.",
                overflow: TextOverflow.ellipsis,
                style: TextStyle(fontSize: 9),
              ),
            ):
           Tooltip(
             message: "Last synced: $lastSyncDate",
             child: Text("Last synced: $lastSyncDate",
               overflow: TextOverflow.ellipsis,
               style: TextStyle(fontSize: 9),
             ),
           ),
        ],
      ),
    ): SizedBox();
  }
}

