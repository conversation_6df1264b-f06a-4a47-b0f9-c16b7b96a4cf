
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/utils/colorful_tag.dart';
import 'package:colorize_text_avatar/colorize_text_avatar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/db/drift/database.dart';
import '../../../core/exceptions/custom_exception.dart';
import '../../../core/providers/client/ClientsRequest.dart';
import '../../../core/providers/client/PaginatedResPartnerTableData.dart';
import '../../../core/providers/client/client_provider.dart';
import '../../../core/repositories/drift/repository_provider_riverpod.dart';
import '../../../core/utils/responsive.dart';
import '../../dashboard/components/error_page.dart';
import '../../dashboard/components/pagination_widget.dart';
import '../clients_home_screen.dart';
import '../edit/client_home_screen.dart';



class ClientList extends ConsumerStatefulWidget {
  @override
  _ClientListState createState() => _ClientListState();
}

class _ClientListState extends ConsumerState<ClientList> {

  List<ResPartnerTableData> clients = [];
  ClientsRequest creq =  new ClientsRequest();

  PaginatedResPartnerTableData res = new PaginatedResPartnerTableData(content: [], totalItems: 0, offset: 0, itemCount: 0, page_number: 0);

  refreshProviders(request){
    creq = request;
    ref.refresh(clientsProvider(creq).future);
    setState(() {

    });
  }

  Future<void> _initClients() async {
    ref.refresh(clientsProvider(creq).future);
    setState(() {

    });
  }



  @override
  void initState() {
    super.initState();
    _initClients();
  }

  @override
  Widget build(BuildContext context) {
      clients.removeWhere((element) => element.name == null);
      var cscrollController = new ScrollController();


      DataRow clientDataRow(ResPartnerTableData userInfo, BuildContext context) {

        deleteDialog(){
          showDialog(
              context: context,
              builder: (_) {
                return AlertDialog(
                    title: Center(
                      child: Text("Confirm Deletion"),
                    ),
                    content: SingleChildScrollView(
                      child: Container(
                        // color:  Theme.of(context).colorScheme.surface,
                        // height: 70,
                        child: Column(
                          children: [
                            Text(
                                "Are you sure want to delete '${userInfo.name}'?"),
                            SizedBox(
                              height: 16,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                ElevatedButton.icon(
                                    icon: Icon(
                                      Icons.close,
                                      size: 14,
                                    ),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.grey),
                                    onPressed: () {
                                      context.pop();
                                    },
                                    label: Text("Cancel")),
                                SizedBox(
                                  width: 20,
                                ),
                                ElevatedButton.icon(
                                    icon: Icon(
                                      Icons.delete,
                                      size: 14,
                                    ),
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red),
                                    onPressed: () async {
                                      try{
                                        await ref.read(resPartnerRepositoryProvider).delete(userInfo.id);
                                        ref.refresh(clientsProvider(creq));

                                        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                          content: Text("Client deleted successfully"),
                                        ));
                                      }catch(e,st) {
                                        print('$e \n$st');
                                        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                          content: Text("An error occurred while deleting"),
                                        ));
                                      }
                                      context.pop();
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(builder: (context) => ClientsHomeScreen()),
                                      );
                                    },
                                    label: Text("Delete"))
                              ],
                            )
                          ],
                        ),
                      ),
                    ));
              });
        }

        return DataRow(
          cells: [

            DataCell(
                Row(
                  children: [
                    TextAvatar(
                      size: 35,
                      backgroundColor: Colors.white,
                      textColor: Colors.white,
                      fontSize: 14,
                      upperCase: true,
                      numberLetters: 1,
                      shape: Shape.Rectangle,
                      text: userInfo.name != null  ?  RegExp(r'^[A-Za-z_.]+$').hasMatch(userInfo.name![0]) ? userInfo.name!: 'a' : "a",
                    ),
                    SizedBox(width: 4,),
                    Container(
                        padding: EdgeInsets.all(5),

                        child: Text(userInfo.name != null ? userInfo.name! : ""))
                  ],)

            ),
            !Responsive.isMobile(context)
                ? DataCell(Container(
                padding: EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(
                    getRoleColor(userInfo.phone).r.toInt(),
                    getRoleColor(userInfo.phone).g.toInt(),
                    getRoleColor(userInfo.phone).b.toInt(),
                    0.2,
                  ),
                  border: Border.all(color: getRoleColor(userInfo.name)),
                  borderRadius: BorderRadius.all(Radius.circular(5.0) //
                  ),
                ),
                child: Text(userInfo.phone != null ? userInfo.phone! : "")))
                : DataCell(Text("")),
            DataCell(Text(userInfo.city != null ? userInfo.city! : "")),
            // DataCell(Text(userInfo.city != null ? userInfo.city! : "")),
            DataCell(
              Row(
                children: [
                  Responsive.isDesktop(context) ? ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: mainColor,
                    ),
                    icon: Icon(
                      Icons.edit,
                      size: 14,
                    ),
                    onPressed: () {
                      Navigator.of(context).push(new MaterialPageRoute<Null>(
                          builder: (BuildContext context) {
                            // return new ClientViewHome(title: "Edit Client", code: "edit", clientId: userInfo.id );
                            return new ClientHome(title: "Edit Client", code: "edit", clientId: userInfo.id );
                          },
                          fullscreenDialog: true));
                    },
                    // Edit
                    label: Text("Edit"),
                  ) :

                  GestureDetector(
                    onTap:(){
                      Navigator.of(context).push(new MaterialPageRoute<Null>(
                          builder: (BuildContext context) {
                            // return new ClientViewHome(title: "Edit Client", code: "edit", clientId: userInfo.id );
                            return new ClientHome(title: "Edit Client", code: "edit", clientId: userInfo.id );
                          },
                          fullscreenDialog: true));
                    },
                    child:Icon(Icons.edit, color:mainColor),
                  ),
                  SizedBox(
                    width: 6,
                  ),
                  // Responsive.isDesktop(context) ? ElevatedButton.icon(
                  //   style: ElevatedButton.styleFrom(
                  //     primary: Colors.green.withOpacity(0.5),
                  //   ),
                  //   icon: Icon(
                  //     Icons.visibility,
                  //     size: 14,
                  //   ),
                  //   onPressed: () {},
                  //   //View
                  //   label: Text("View"),
                  // ) : Icon(Icons.remove_red_eye, color: Colors.green.withOpacity(0.5)),
                  SizedBox(
                    width: 6,
                  ),
                  Responsive.isDesktop(context)
                      ? ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: dangerColor,
                    ),
                    icon: Icon(Icons.delete),
                    onPressed: () {
                      deleteDialog();
                    },
                    // Delete
                    label: Text("Delete"),
                  )
                      : GestureDetector(
                      onTap: (){
                        deleteDialog();
                      } ,
                      child: Icon( Icons.delete, color: dangerColor,)
                  ),
                ],
              ),
            ),
          ],
        );
      }


      return Container(
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Client List",
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Scrollbar(
              controller: cscrollController,
              thumbVisibility: true, //always show scrollbar
              thickness: 10, //width of scrollbar
              radius: Radius.circular(20), //corner radius of scrollbar
              scrollbarOrientation: ScrollbarOrientation.bottom, //which side to show scrollbar
              child: Padding(
                padding: EdgeInsets.only(bottom: 20 ),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  controller: cscrollController,
                  child: ConstrainedBox(
                    constraints: new BoxConstraints(
                      minWidth: Responsive.isDesktop(context)?((MediaQuery.of(context).size.width/6)*5)-50:(MediaQuery.of(context).size.width-60),
                    ),
                    child: ref.watch(clientsProvider(creq)).when(
                      data: (data){
                        res = data;
                        clients = data.content;
                        return new DecoratedBox(
                          decoration: new BoxDecoration(),
                          child: DataTable(
                            horizontalMargin: 0,
                            columnSpacing: defaultPadding,
                            columns: [
                              DataColumn(
                                label: Text("Name"),
                              ),
                              !Responsive.isMobile(context)?DataColumn(
                                label: Text("Phone"),):
                              DataColumn(label: Text("")),
                              DataColumn(
                                label: Text("City"),
                              ),
                              // DataColumn(
                              //   label: Text("Stage"),
                              // ),
                              DataColumn(
                                label: Text("Operation"),
                              ),
                            ],
                            rows: List.generate(
                              clients.length,
                                  (index) => clientDataRow(clients[index], context),
                            ),
                          ),
                        );
                      },
                      loading: () =>
                          Padding(
                            padding: EdgeInsets.all(10),
                            child: Center(child: CircularProgressIndicator()),
                          ),
                      error: (e, st) => ErrorPage(
                        error: e is CustomException ? e.message : e.toString(),
                        onTryAgain: () => setState,
                      ),
                    ),
                  ),
                ),
              )
          ),
          PaginationWidget(res: res, req:creq, getItems: refreshProviders),

        ],
      ),
    );
  }


}
