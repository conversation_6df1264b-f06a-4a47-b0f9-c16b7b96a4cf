import 'dart:convert';
import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';

/// This file contains extension methods for AccountMoveLineTableData
/// It replaces the old AccountMoveLine class with direct use of Drift's generated classes

/// Extension methods for AccountMoveLineTableData
extension AccountMoveLineTableDataExtensions on AccountMoveLineTableData {
  /// Check if the line is active (not deleted)
  bool isActive() => !is_deleted;

  /// Get the total price (quantity * price_unit)
  double getTotalPrice() {
    if (quantity == null || price_unit == null) return 0.0;
    return quantity! * price_unit!;
  }

  /// Get the discounted price
  double getDiscountedPrice() {
    if (quantity == null || price_unit == null) return 0.0;
    if (discount == null || discount == 0) return quantity! * price_unit!;
    return quantity! * price_unit! * (1 - discount! / 100);
  }

  /// Get the line subtotal (same as getDiscountedPrice for compatibility)
  double getSubtotal() => getDiscountedPrice();

  /// Get tax IDs as a list
  List<int> getTaxIds() {
    if (tax_ids == null || tax_ids!.isEmpty) return [];
    try {
      final List<dynamic> taxIds = json.decode(tax_ids!);
      return taxIds.map((id) => id as int).toList();
    } catch (e) {
      return [];
    }
  }

  /// Check if the line has taxes
  bool hasTaxes() => getTaxIds().isNotEmpty;

  /// Check if the line has a product
  bool hasProduct() => product_id != null;

  /// Get the line total with taxes (if price_total is available)
  double getTotalWithTaxes() {
    return price_total ?? getSubtotal();
  }

  /// Get tax amount (total - subtotal)
  double getTaxAmount() {
    return getTotalWithTaxes() - getSubtotal();
  }

  /// Check if this is a tax line (tax_line_id is set)
  bool isTaxLine() => tax_line_id != null;

  /// Convert to AccountMoveLineTableCompanion for Drift operations
  AccountMoveLineTableCompanion toCompanion() {
    return AccountMoveLineTableCompanion(
      id: Value(id),
      move_id: Value(move_id),
      name: Value(name),
      product_id: Value(product_id),
      account_id: Value(account_id),
      partner_id: Value(partner_id),
      quantity: Value(quantity),
      price_unit: Value(price_unit),
      discount: Value(discount),
      price_subtotal: Value(price_subtotal),
      price_total: Value(price_total),
      balance: Value(balance),
      amount_currency: Value(amount_currency),
      debit: Value(debit),
      credit: Value(credit),
      currency_id: Value(currency_id),
      tax_base_amount: Value(tax_base_amount),
      tax_line_id: Value(tax_line_id),
      tax_group_id: Value(tax_group_id),
      tax_ids: Value(tax_ids),
      date: Value(date),
      date_maturity: Value(date_maturity),
      company_id: Value(company_id),
      company_currency_id: Value(company_currency_id),
      sequence: Value(sequence),
      display_type: Value(display_type),
      universal_id: Value(universal_id),
      is_synced: Value(is_synced),
      origin_id: Value(origin_id),
      version: Value(version),
      is_confirmed: Value(is_confirmed),
      is_deleted: Value(is_deleted),
    );
  }

  /// Create a copy with updated fields
  AccountMoveLineTableData copyWith({
    int? id,
    int? move_id,
    String? name,
    int? product_id,
    int? account_id,
    int? partner_id,
    double? quantity,
    double? price_unit,
    double? discount,
    double? price_subtotal,
    double? price_total,
    double? balance,
    double? amount_currency,
    double? debit,
    double? credit,
    int? currency_id,
    int? tax_base_amount,
    int? tax_line_id,
    int? tax_group_id,
    String? tax_ids,
    String? date,
    String? date_maturity,
    int? company_id,
    int? company_currency_id,
    int? sequence,
    String? display_type,
    int? universal_id,
    bool? is_synced,
    int? origin_id,
    int? version,
    bool? is_confirmed,
    bool? is_deleted,
  }) {
    return AccountMoveLineTableData(
      id: id ?? this.id,
      move_id: move_id ?? this.move_id,
      name: name ?? this.name,
      product_id: product_id ?? this.product_id,
      account_id: account_id ?? this.account_id,
      partner_id: partner_id ?? this.partner_id,
      quantity: quantity ?? this.quantity,
      price_unit: price_unit ?? this.price_unit,
      discount: discount ?? this.discount,
      price_subtotal: price_subtotal ?? this.price_subtotal,
      price_total: price_total ?? this.price_total,
      balance: balance ?? this.balance,
      amount_currency: amount_currency ?? this.amount_currency,
      debit: debit ?? this.debit,
      credit: credit ?? this.credit,
      currency_id: currency_id ?? this.currency_id,
      tax_base_amount: tax_base_amount ?? this.tax_base_amount,
      tax_line_id: tax_line_id ?? this.tax_line_id,
      tax_group_id: tax_group_id ?? this.tax_group_id,
      tax_ids: tax_ids ?? this.tax_ids,
      date: date ?? this.date,
      date_maturity: date_maturity ?? this.date_maturity,
      company_id: company_id ?? this.company_id,
      company_currency_id: company_currency_id ?? this.company_currency_id,
      sequence: sequence ?? this.sequence,
      display_type: display_type ?? this.display_type,
      universal_id: universal_id ?? this.universal_id,
      is_synced: is_synced ?? this.is_synced,
      origin_id: origin_id ?? this.origin_id,
      version: version ?? this.version,
      is_confirmed: is_confirmed ?? this.is_confirmed,
      is_deleted: is_deleted ?? this.is_deleted,
    );
  }


}

/// Extension methods for AccountMoveLineTableCompanion
extension AccountMoveLineTableCompanionExtensions on AccountMoveLineTableCompanion {
  /// Create a companion for a new invoice line
  static AccountMoveLineTableCompanion createInvoiceLine({
    required int move_id,
    String? name,
    int? product_id,
    double? quantity = 1.0,
    double? price_unit = 0.0,
    double? discount = 0.0,
  }) {
    return AccountMoveLineTableCompanion.insert(
      move_id: Value(move_id),
      name: Value(name),
      product_id: Value(product_id),
      quantity: Value(quantity),
      price_unit: Value(price_unit),
      discount: Value(discount),
      is_synced: const Value(false),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
  }

  /// Mark a line as deleted
  AccountMoveLineTableCompanion markAsDeleted() {
    return copyWith(
      is_deleted: const Value(true),
    );
  }

  /// Mark a line as active
  AccountMoveLineTableCompanion markAsActive() {
    return copyWith(
      is_deleted: const Value(false),
    );
  }

  /// Mark a line as synced
  AccountMoveLineTableCompanion markAsSynced(int universal_id) {
    return copyWith(
      universal_id: Value(universal_id),
      is_synced: const Value(true),
    );
  }

  /// Set tax IDs from a list
  AccountMoveLineTableCompanion setTaxIds(List<int> taxIds) {
    return copyWith(
      tax_ids: Value(json.encode(taxIds)),
    );
  }

  /// Update prices based on tax calculation
  AccountMoveLineTableCompanion updatePrices({
    required double subtotal,
    required double total,
  }) {
    return copyWith(
      price_subtotal: Value(subtotal),
      price_total: Value(total),
    );
  }

  /// Mark as tax line
  AccountMoveLineTableCompanion markAsTaxLine(int taxId) {
    return copyWith(
      tax_line_id: Value(taxId),
      display_type: const Value('tax'),
    );
  }
}

/// Model class for an invoice line with its related product
class AccountMoveLineWithProduct {
  final AccountMoveLineTableData line;
  final ProductProductTableData? product;

  AccountMoveLineWithProduct({
    required this.line,
    this.product,
  });
}
