import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:drift/drift.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:invoicer/core/db/drift/database_service.dart';
import 'package:invoicer/core/models/interfaces/DriftSyncClass.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/network/odoo_client.dart';
import 'package:invoicer/core/utils/odoo_mapper.dart';
import 'package:invoicer/core/models/drift/DriftSyncWrappers.dart';
import 'package:invoicer/core/utils/invoicegenerator.dart'; // For getDownloadPath2
import '../../../types/syncresult.dart';
import '../../../utils/UserPreference.dart';

class SyncMaster {
  late SharedPreferences prefs;
  DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
  var lastSyncDate;
  SyncResult syncres = new SyncResult();
  var userId;

  // Temporary storage for invoice line items during sync
  final Map<String, List<Map<String, dynamic>>> _tempLineItemsStorage = {};

  // Initialize sync process
  Future<SyncResult> initGetSync() async {
    prefs = await SharedPreferences.getInstance();

    // Get the last sync date or default to 30 days ago
    String? storedLastSyncDate = await prefs.getString(UserPreference.lastSyncDate);

    // Validate the stored date to ensure it's not in the future
    if (storedLastSyncDate != null && storedLastSyncDate.isNotEmpty) {
      try {
        DateTime parsedDate = dateFormat.parse(storedLastSyncDate);
        // Check if the date is not in the future
        if (parsedDate.isBefore(DateTime.now())) {
          // Subtract 5 days from the last sync date to ensure we don't miss any records
          DateTime adjustedDate = parsedDate.subtract(Duration(days: 5));
          lastSyncDate = dateFormat.format(adjustedDate);
          print("Original stored sync date: $storedLastSyncDate");
          print("Using sync date with 5-day lookback: $lastSyncDate");
        } else {
          print("WARNING: Stored sync date is in the future: $storedLastSyncDate");
          // Reset to 30 days ago if date is in the future
          lastSyncDate = dateFormat.format(DateTime.now().subtract(Duration(days: 30)));
        }
      } catch (e,st) {
        print('$e \n$st');
        print("ERROR: Invalid stored sync date format: $storedLastSyncDate");
        // Reset to 30 days ago if date format is invalid
        lastSyncDate = dateFormat.format(DateTime.now().subtract(Duration(days: 30)));
      }
    } else {
      // No stored date, default to 30 days ago
      lastSyncDate = dateFormat.format(DateTime.now().subtract(Duration(days: 30)));
    }

    // If we don't have a stored date or it was invalid, use the current time as the new sync date
    // Otherwise, keep using the original stored date (not the adjusted one with 5-day lookback)
    // This ensures we don't keep going further back in time with each sync
    String currentDateTime = dateFormat.format(DateTime.now());

    if (storedLastSyncDate == null || storedLastSyncDate.isEmpty) {
      // No stored date, save current time
      await prefs.setString(UserPreference.lastSyncDate, currentDateTime);
    } else {
      try {
        // Verify the stored date is valid by parsing it
        DateTime parsedDate = dateFormat.parse(storedLastSyncDate);

        // Check if the date is in the future
        if (parsedDate.isAfter(DateTime.now())) {
          // Date is in the future, save current time instead
          await prefs.setString(UserPreference.lastSyncDate, currentDateTime);
        } else {
          // Date is valid and not in the future, keep it
          await prefs.setString(UserPreference.lastSyncDate, storedLastSyncDate);
        }
      } catch (e,st) {
        print('$e \n$st');
        // Invalid date format, save current time
        await prefs.setString(UserPreference.lastSyncDate, currentDateTime);
      }
    }
    print("Using sync date: $lastSyncDate");

    // Get Odoo credentials
    userId = (await prefs.getString(UserPreference.userId));
    final String odooUrl = prefs.getString('odooUrl') ?? '';
    final String database = prefs.getString('database') ?? '';

    // Check if we have the necessary credentials
    if (userId == null || odooUrl.isEmpty || database.isEmpty) {
      syncres.success = false;
      syncres.code = 1; // login required
      syncres.message = 'Login required or missing Odoo credentials';
      return syncres;
    }

    syncres.success = true;
    return syncres;
  }


  // Save data received from Odoo to local database
  Future<bool> saveReceivedData(DriftSyncClass? localData, DriftSyncClass newData) async {
      await newData.saveSynced(localData);


    // Special handling for invoices to process line items
    if (newData is AccountMoveSync) {
      await _processInvoiceLineItems(newData);
    }

    syncres.success = true;
    return true;
  }

  // Process invoice line items from Odoo
  Future<void> _processInvoiceLineItems(AccountMoveSync invoiceSync) async {
    final db = DatabaseService().database;
    final invoice = invoiceSync.move;

    // Skip if the invoice doesn't have a valid ID
    if (invoice.id <= 0) {
      print("WARNING: Invoice has invalid ID: ${invoice.id}");
      return;
    }

    // Check if we have line items data in the temporary storage
    final String cacheKey = "invoice_line_items_${invoice.universal_id}";
    final List<Map<String, dynamic>>? lineItemsData = _tempLineItemsStorage[cacheKey];

    if (lineItemsData == null || lineItemsData.isEmpty) {
      print("No line items found for invoice with ID: ${invoice.id}");
      return;
    }

    print("Processing ${lineItemsData.length} line items for invoice with ID: ${invoice.id}");

    for (var lineData in lineItemsData) {
      try {
        // Extract the universal_id (Odoo ID) for the line item
        final int? lineUniversalId = lineData['id'];
        if (lineUniversalId == null) {
          print("WARNING: Line item has no ID, skipping");
          continue;
        }

        // --- PRODUCT ID MAPPING LOGIC START ---
        dynamic productField = lineData['product_id'];
        int? localProductId;
        if (productField is List && productField.isNotEmpty) {
          // Odoo product_id is a list: [universal_id, name]
          int odooProductUniversalId = productField[0];
          final localProduct = await db.productProductDao.getProductByuniversal_id(odooProductUniversalId);
          if (localProduct != null) {
            localProductId = localProduct.id;
          } else {
            print("WARNING: No local product found for universal_id: $odooProductUniversalId");
            localProductId = null;
          }
        } else if (productField is int) {
          // Already an int, try to map
          final localProduct = await db.productProductDao.getProductByuniversal_id(productField);
          localProductId = localProduct?.id;
        } else {
          localProductId = null;
        }
        // --- PRODUCT ID MAPPING LOGIC END ---

        // Check if this line item already exists in our database
        final existingLine = await db.accountMoveLineDao.getLineByuniversal_id(lineUniversalId);

        if (existingLine != null) {
          // Update existing line item
          print("Updating existing line item with universal_id: $lineUniversalId");

          // Create updated line data with Value objects for Drift
          final updatedLine = AccountMoveLineTableCompanion(
            id: Value(existingLine.id),
            move_id: Value(invoice.id), // Ensure it's linked to the correct invoice
            name: Value(lineData['name']),
            product_id: Value(localProductId),
            // account_id: Value(lineData['account_id']),
            // partner_id: Value(lineData['partner_id']),
            quantity: Value(lineData['quantity']),
            price_unit: Value(lineData['price_unit']),
            discount: Value(lineData['discount']),
            price_subtotal: Value(lineData['price_subtotal']),
            price_total: Value(lineData['price_total']),
            universal_id: Value(lineUniversalId),
            is_synced: const Value(true),
            is_confirmed: const Value(true),
            is_deleted: const Value(false),
          );

          // Save the updated line
          await db.accountMoveLineDao.insertOrUpdateLine(updatedLine);
        } else {
          // Create new line item
          print("Creating new line item with universal_id: $lineUniversalId");

          // Create new line data
          final newLine = AccountMoveLineTableCompanion.insert(
            move_id: Value(invoice.id), // Link to the invoice
            name: Value(lineData['name']),
            product_id: Value(localProductId),
            // account_id: Value(lineData['account_id']),
            // partner_id: Value(lineData['partner_id']),
            quantity: Value(lineData['quantity'] ?? 1.0),
            price_unit: Value(lineData['price_unit'] ?? 0.0),
            discount: Value(lineData['discount'] ?? 0.0),
            price_subtotal: Value(lineData['price_subtotal']),
            price_total: Value(lineData['price_total']),
            universal_id: Value(lineUniversalId),
            is_synced: const Value(true),
            is_confirmed: const Value(true),
            is_deleted: const Value(false),
            version: const Value(1),
          );

          // Save the new line
          await db.accountMoveLineDao.insertOrUpdateLine(newLine);
          print("Created new line item with universal_id: $lineUniversalId");
        }
      } catch (e, st) {
        print("Error processing line item: $e \nStack trace: $st");
      }
    }

    // Clear the temporary storage for this invoice
    _tempLineItemsStorage.remove(cacheKey);
  }

  // Fetch models from Odoo and save to local database
  Future<SyncResult> fetchAndSaveModels(String modelType,  fullSync ) async {
    SyncResult initResult = await initGetSync();
    if (!initResult.success) {
      return initResult; // Return early if initialization failed
    }

    print("Fetching ${modelType} from Odoo");

    try {
      // Get the Odoo model name for this type
      final odooModel = OdooMapper.modelMapping[modelType];
      if (odooModel == null) {
        syncres.success = false;
        syncres.message = 'Unknown entity type: $modelType';
        return syncres;
      }

      // For businesses, check if we already have a company
      if (modelType == 'businesses') {
        final db = DatabaseService().database;
        final existingCompanies = await db.resCompanyDao.getActiveCompanies();
        if (existingCompanies.isNotEmpty) {
          print("User already has a company. Skipping sync of additional companies.");
          syncres.success = true;
          syncres.message = "User already has a company. Skipping sync of additional companies.";
          return syncres;
        }
      }

      // Create a domain filter for records
      print("Using last sync date for filter: $lastSyncDate");
      List<List<dynamic>> domain = [];

      // Special handling for currencies and categories - don't use write_date filter
      if (modelType == 'currencies') {
        // For currencies, we want to get ALL currencies (active and inactive) regardless of write_date
        // This allows us to properly handle deactivated currencies
        domain = [];
      } else if (modelType == 'categories') {
        // For categories, use an empty domain to get all categories
        // The 'active' field is not valid for product.category in this Odoo instance
        domain = [];
      } else if (!fullSync) {
        // For all other entity types, use the write_date filter unless fullSync is true
        domain = [['write_date', '>=', lastSyncDate]];

        // Add additional filters for specific entity types
        if (modelType == 'clients') {
          // Only sync partners that are customers (customer_rank > 0)
          domain.add(['customer_rank', '>', 0]);
        } else if (modelType == 'invoices') {
          // Only sync account.move records that are invoices
          domain.add(['move_type', 'in', ['out_invoice']]);
        }
      } else {
        // For fullSync, fetch all records (no write_date filter)
        if (modelType == 'clients') {
          domain = [['customer_rank', '>', 0]];
        } else if (modelType == 'invoices') {
          domain = [['move_type', 'in', ['out_invoice']]];
        } else {
          domain = [];
        }
      }

      // Get all fields for the model
      List<String> fields = _getFieldsForType(modelType);

      print("Fetching ${modelType} from Odoo");

      List<Map<String, dynamic>> result = [];

      try {
        // Fetch data from Odoo
        int? limit;
        if (modelType == 'invoices') {
          limit = 100;
        }
        result = await OdooClient.instance.searchRead(
          odooModel,
          domain,
          fields,
          limit: limit??0,
        );
      } catch (e,st) {
        print('$e \n$st');
        // If there's an error with the fields, try with just the ID field
        if (e.toString().contains("Invalid field")) {
          print("Retrying ${modelType} with minimal fields");
          try {
            result = await OdooClient.instance.searchRead(
              odooModel,
              domain,
              ['id', 'name'] // Minimal fields that should exist on all models
            );
          } catch (e2,st) {
            print('$e \n$st');
            print("Error fetching ${modelType} with minimal fields: $e2");
            syncres.success = false;
            syncres.message = "Error: $e2";
            return syncres;
          }
        } else {
          print("Error fetching ${modelType}: $e");
          syncres.success = false;
          syncres.message = "Error: $e";
          return syncres;
        }
      }

      print("Received ${result.length} ${modelType} from Odoo");

      if (result.isEmpty) {
        print("No ${modelType} found in Odoo matching the criteria");

        // For categories, try a more permissive search to check if any categories exist at all
        if (modelType == 'categories') {
          try {
            final checkResult = await OdooClient.instance.searchRead(
              odooModel,
              [], // Empty domain to get all categories
              ['id', 'name'],
              limit: 5 // Just get a few to check
            );

            if (checkResult.isNotEmpty) {
              print("Categories exist in Odoo but none match the current filter criteria");
            } else {
              print("No categories found in Odoo at all");
            }
          } catch (e,st) {
            print('$e \n$st');
            print("Error checking categories: $e");
          }
        }

        syncres.success = true;
        return syncres;
      }

      // Process each record
      int successCount = 0;
      int errorCount = 0;

      for (var odooRecord in result) {
        try {
          // Convert Odoo record to app model using direct JSON conversion
          final appModel = await _convertOdooRecordToAppModel(modelType, odooRecord);

          // Find local copy if it exists
          DriftSyncClass? localCopy = await appModel.getByUni();
          appModel.is_synced = true;

          // Save to local database
          await saveReceivedData(localCopy, appModel);

          successCount++;
        } catch (e,st) {
          print('$e \n$st');
          errorCount++;
          print("Error processing record: $e");

          // If too many errors, abort the sync
          if (errorCount > result.length / 2) { // If more than 50% of records fail
            syncres.success = false;
            syncres.message = "Too many errors processing ${modelType}. Sync aborted.";
            return syncres;
          }
        }
      }

      print("Sync of ${modelType} complete: $successCount successful, $errorCount failed");

      // Special post-processing for currencies to handle deactivated ones
      if (modelType == 'currencies') {
        await handleDeactivatedCurrencies(result);
      }

      // If we had some errors but not enough to abort, still mark as success but include warning
      if (errorCount > 0) {
        syncres.success = true;
        syncres.message = "Completed with $errorCount errors out of ${result.length} records";
      } else {
        syncres.success = true;
        syncres.message = "Successfully synced $successCount records";
      }

      return syncres;
    } catch (e,st) {
      print('$e \n$st');
      print("Error fetching ${modelType} from Odoo: $e");
      syncres.success = false;
      syncres.message = "Error: $e";
      return syncres;
    }
  }

  // Send local models to Odoo
  Future<SyncResult> sendModelsToOdoo(String modelType, List<DriftSyncClass> modelsToSync) async {
    print("${modelType} sync to Odoo begins with: ${modelsToSync.length} items");

    try {
      // Get the Odoo model name for this type
      final odooModel = OdooMapper.modelMapping[modelType];
      if (odooModel == null) {
        syncres.success = false;
        syncres.message = 'Unknown entity type: $modelType';
        return syncres;
      }

      if (modelsToSync.isEmpty) {
        print("No ${modelType} to sync to Odoo");
        syncres.success = true;
        syncres.message = "Nothing to sync";
        return syncres;
      }

      // Process each model
      int successCount = 0;
      int errorCount = 0;
      List<String> errorMessages = [];

      for (var model in modelsToSync) {
        try {
          print("PROCESSING MODEL FOR SYNC: ${model.runtimeType} with ID: ${model.id}, universal_id: ${model.universal_id}");

          // Convert to JSON for Odoo
          final odooValues = await model.toSyncJson();
          print("CONVERTED TO ODOO FORMAT: ${jsonEncode(odooValues)}");

          // Create or update in Odoo
          if (model.universal_id == null) {
            print("CREATING NEW RECORD IN ODOO");
            // Create new record in Odoo
            final newId = await OdooClient.instance.create(odooModel, odooValues);

            // Update with new ID from Odoo
            model.universal_id = newId;
            model.is_synced = true;
            await model.saveSynced(null);
            print("CREATED NEW RECORD IN ODOO WITH ID: $newId");
          } else {
            print("UPDATING EXISTING RECORD IN ODOO WITH ID: ${model.universal_id}");
            // Update existing record in Odoo
            await OdooClient.instance.write(odooModel, [model.universal_id!], odooValues);

            // Update sync status
            model.is_synced = true;
            await model.saveSynced(null);
            print("UPDATED RECORD IN ODOO");
          }

          successCount++;
        } catch (e,st) {
          print('$e \n$st');
          errorCount++;
          print("ERROR SYNCING MODEL TO ODOO: $e");

          // Log more detailed information about the model
          print("MODEL TYPE: ${model.runtimeType}");
          print("MODEL ID: ${model.id}");
          print("UNIVERSAL ID: ${model.universal_id}");

          // Store error message (limit to first 5 errors to avoid excessive messages)
          if (errorMessages.length < 5) {
            errorMessages.add("Error syncing ${model.runtimeType} with ID ${model.id}: $e");
          }

          // If too many errors, abort the sync
          if (errorCount > modelsToSync.length / 2) { // If more than 50% of records fail
            syncres.success = false;
            syncres.message = "Too many errors syncing ${modelType} to Odoo. Sync aborted.";
            return syncres;
          }
        }
      }

      print("${modelType} sync to Odoo complete: $successCount successful, $errorCount failed");

      // If we had some errors but not enough to abort, still mark as success but include warning
      if (errorCount > 0) {
        syncres.success = true;
        syncres.message = "Completed with $errorCount errors out of ${modelsToSync.length} records";
      } else {
        syncres.success = true;
        syncres.message = "Successfully synced $successCount records";
      }

      return syncres;
    } catch (e,st) {
      print('$e \n$st');
      print("Error in sync to Odoo: $e");
      syncres.success = false;
      syncres.message = "Error: $e";
      return syncres;
    }
  }

  // Get models that need to be synced to Odoo
  Future<List<DriftSyncClass>> getReadyForSync(String type) async {
    print("Getting ${type} ready for sync to Odoo");

    try {
      final db = DatabaseService().database;

      switch (type) {
        case 'businesses':
          // Get only the first/active company that needs to be synced
          final companies = await db.resCompanyDao.getUnsyncedCompanies();
          print("Found ${companies.length} businesses ready for sync");

          // If there are multiple companies, only sync the first one
          if (companies.length > 1) {
            print("Multiple companies found. Only syncing the first company.");

            // Get the active company ID from SharedPreferences
            SharedPreferences prefs = await SharedPreferences.getInstance();
            int? activeBusiness = prefs.getInt(UserPreference.activeBusiness);

            if (activeBusiness != null) {
              // Find the active company in the list
              final activeCompany = companies.firstWhere(
                (company) => company.id == activeBusiness,
                orElse: () => companies.first
              );

              print("Syncing active company with ID: ${activeCompany.id}");
              return [ResCompanySync(activeCompany)];
            } else {
              // If no active company is set, just use the first one
              print("No active company set. Syncing the first company with ID: ${companies.first.id}");
              return [ResCompanySync(companies.first)];
            }
          }

          return companies.map((company) => ResCompanySync(company)).toList();

        case 'clients':
          // Get all clients that need to be synced
          final partners = await db.resPartnerDao.getUnsyncedPartners();
          print("Found ${partners.length} clients ready for sync");
          return partners.map((partner) => ResPartnerSync(partner)).toList();

        case 'categories':
          // Get all categories that need to be synced
          final categories = await db.productCategoryDao.getUnsyncedCategories();
          print("Found ${categories.length} categories ready for sync");
          return categories.map((category) => ProductCategorySync(category)).toList();

        case 'products':
          // Get all products that need to be synced
          final products = await db.productProductDao.getUnsyncedProducts();
          print("Found ${products.length} products ready for sync");
          return products.map((product) => ProductProductSync(product)).toList();

        case 'invoices':
          // Get all invoices that need to be synced
          final invoices = await db.accountMoveDao.getUnsyncedInvoices();
          print("Found ${invoices.length} invoices ready for sync");
          return invoices.map((invoice) => AccountMoveSync(invoice)).toList();

        case 'currencies':
          // Get all currencies that need to be synced
          final currencies = await db.resCurrencyDao.getUnsyncedCurrencies();
          print("Found ${currencies.length} currencies ready for sync");
          return currencies.map((currency) => ResCurrencySync(currency)).toList();

        default:
          print("Unknown entity type: $type");
          return [];
      }
    } catch (e) {
      print("Error getting ${type} ready for sync: $e");
      return [];
    }
  }

  // Utility function to map Odoo company_id (universal_id) to local company_id
  Future<int?> _mapOdooCompanyIdToLocal(int odooCompanyId) async {
    final db = DatabaseService().database;
    final localCompany = await db.resCompanyDao.getCompanyByuniversal_id(odooCompanyId);

    if (localCompany != null) {
      print("Mapped Odoo company_id $odooCompanyId to local company_id ${localCompany.id}");
      return localCompany.id;
    } else {
      print("WARNING: Could not find local company with universal_id $odooCompanyId");
      return null;
    }
  }

  // Convert Odoo record to app model
  Future<DriftSyncClass> _convertOdooRecordToAppModel(String type, Map<String, dynamic> odooRecord) async {
    // Pre-process the Odoo record to handle 'false' values
    Map<String, dynamic> processedRecord = Map.from(odooRecord);

    // Special handling for invoice line items
    if (type == 'invoices' && processedRecord.containsKey('invoice_line_ids') && processedRecord['invoice_line_ids'] != false) {
      // Extract the invoice line items
      final List<dynamic> lineIds = processedRecord['invoice_line_ids'];

      // Store the line items in temporary storage for later processing
      if (lineIds.isNotEmpty && processedRecord.containsKey('id')) {
        final int invoiceId = processedRecord['id'];
        final String cacheKey = "invoice_line_items_$invoiceId";

        // Fetch the line items data from Odoo
        try {
          final lineItems = await OdooClient.instance.searchRead(
            'account.move.line',
            [['id', 'in', lineIds]],
            [
              'id', 'name', 'product_id', 'account_id','move_id', 'partner_id',
              'quantity', 'price_unit', 'discount', 'price_subtotal', 'price_total',
              'tax_ids', 'currency_id'
            ]
          );

          // Store the line items for later processing
          _tempLineItemsStorage[cacheKey] = lineItems;
          print("Received ${lineItems.length} line items for invoice with ID: $invoiceId");
        } catch (e) {
          print("Error fetching line items for invoice with ID $invoiceId: $e");
        }
      }

      // Remove the invoice_line_ids field as it's not part of the AccountMoveTableData model
      processedRecord.remove('invoice_line_ids');
    }

    // Fields that should actually be boolean values in the app's database
    final booleanFields = [
      // App-specific boolean fields
      'is_synced', 'is_confirmed', 'is_deleted', 'is_order',

      // Odoo boolean fields that are stored as boolean in our database
      'purchase_ok', 'active',

      // Odoo boolean fields that are stored as integers in our database
      // These should NOT be included here as they need to be converted to null
      // 'is_company', 'sale_ok',
      // 'reconcile', 'auto_post', 'tax_exigible', 'always_set_currency_id',
      // 'include_initial_balance', 'force_delete'
    ];

    // Process each field
    Map<String, dynamic> updatedRecord = {};
    processedRecord.forEach((key, value) {
      // Special handling for product categories
      if (type == 'categories' && key == 'parent_id') {
        // For product categories, parent_id can come in different formats
        if (value == false) {
          // If parent_id is false, it means this is a top-level category
          updatedRecord[key] = null;
        } else if (value is List) {
          // If parent_id is a list [id, name], extract the ID
          if (value.length >= 1 && value[0] != false) {
            updatedRecord[key] = value[0];
          } else {
            updatedRecord[key] = null;
          }
        } else if (value is int) {
          // If parent_id is already an integer, use it directly
          updatedRecord[key] = value;
        } else {
          // For any other format, set to null
          updatedRecord[key] = null;
        }
      } else if (value == false) {
        if (booleanFields.contains(key)) {
          // For actual boolean fields, preserve as false
          updatedRecord[key] = false;
        } else {
          // For all other fields (integers, strings, etc.), convert false to null
          updatedRecord[key] = null;
        }
      } else if (value is List && value.length == 2) {
        // For array fields like [id, name], just use the ID (first element)
        // Handle the case where the first element might be an integer or a boolean (false)
        if (value[0] == false) {
          // If the ID is false, set it to null
          updatedRecord[key] = null;
        } else if (key == 'invoice_payment_term_id' || key == 'invoice_user_id') {
          // For fields that are stored as text in the database but come as [id, name] arrays from Odoo,
          // convert the ID to a string
          updatedRecord[key] = value[0].toString();
        } else {
          // For other fields, use the ID directly
          updatedRecord[key] = value[0];
        }
      } else {
        updatedRecord[key] = value;
      }
    });

    // Ensure required boolean fields are never null
    if (type == 'invoices') {
      updatedRecord['is_order'] = updatedRecord['is_order'] ?? false;
    }

    // Log a warning if company_id is missing for clients or invoices
    if ((type == 'clients' || type == 'invoices') && updatedRecord['company_id'] == null) {
      print("WARNING: $type record with ID ${updatedRecord['id']} has no company_id");
      // We're not manually setting company_id to preserve the original data as received from Odoo
    }

    // Special handling for models with company_id to map Odoo company_id to local company_id
    if ((type == 'clients' || type == 'invoices' || type == 'products' || type == 'categories') && updatedRecord['company_id'] != null) {
      // The company_id from Odoo is actually the universal_id in our local database
      int odooCompanyId = updatedRecord['company_id'];

      // Map Odoo company_id to local company_id
      final localCompanyId = await _mapOdooCompanyIdToLocal(odooCompanyId);
      if (localCompanyId != null) {
        updatedRecord['company_id'] = localCompanyId;
      }
    }

    // Now use the OdooMapper to convert the processed record to an app model
    return OdooMapper.fromOdooModel(type, updatedRecord);
  }

  // Get fields to fetch for each model type
  List<String> _getFieldsForType(String type) {
    // Common fields to include for all entity types
    List<String> commonFields = ['id', 'write_date'];

    // Entity-specific fields
    List<String> specificFields = [];

    switch (type) {
      case 'businesses':
        specificFields = ['name', 'street', 'street2', 'city', 'zip', 'country_id', 'state_id',
                         'phone', 'email', 'website', 'vat', 'company_registry', 'currency_id',
                         'partner_id', 'parent_id', 'sequence', 'logo', 'logo_web', 'color'];
        break;
      case 'clients':
        specificFields = ['name', 'street', 'street2', 'city', 'zip', 'country_id', 'state_id',
                         'phone', 'email', 'website', 'company_id', 'customer_rank', 'is_company',
                         'parent_id', 'vat', 'type', 'user_id', 'comment', 'active'];
        break;
      case 'categories':
        // Only include valid fields for product.category in Odoo
        // Removed 'description', 'company_id', 'active', and 'sequence' as they are not valid fields
        // Keeping only the most basic fields that are guaranteed to exist
        specificFields = ['name', 'complete_name', 'parent_id', 'parent_path'];
        break;
      case 'products':
        specificFields = ['name', 'description', 'list_price', 'categ_id', 'company_id', 'default_code',
                         'barcode', 'active', 'type', 'uom_id', 'uom_po_id'];
        break;
      case 'invoices':
        specificFields = [
          // Basic invoice fields
          'name', 'partner_id', 'invoice_date', 'invoice_date_due', 'date', 'ref', 'narration',
          'currency_id', 'company_id', 'journal_id', 'move_type', 'state',

          // Amount fields
          'amount_untaxed', 'amount_tax', 'amount_total', 'amount_residual',
          'amount_untaxed_signed', 'amount_tax_signed', 'amount_total_signed', 'amount_residual_signed',

          // Payment related fields
          'payment_state', 'payment_reference', 'invoice_payment_term_id',

          // Other invoice fields
          'invoice_user_id', 'invoice_partner_display_name', 'invoice_origin',
          'invoice_cash_rounding_id', 'tax_cash_basis_rec_id', 'tax_cash_basis_origin_move_id',
          'auto_post', 'reversed_entry_id', 'fiscal_position_id', 'invoice_incoterm_id',
          'invoice_source_email',

          // Related records
          'invoice_line_ids', 'payment_id', 'line_ids', 'invoice_payments_widget'
        ];
        break;
      case 'currencies':
        specificFields = ['name', 'symbol', 'full_name', 'rate', 'decimal_places', 'active',
                         'position', 'currency_unit_label', 'currency_subunit_label', 'rounding'];
        break;
      default:
        specificFields = ['name'];
        break;
    }

    // Ensure company_id is included for all entity types except businesses, categories, and currencies
    if (type != 'businesses' && type != 'categories' && type != 'currencies' && !specificFields.contains('company_id')) {
      specificFields.add('company_id');
    }

    // Combine common fields with specific fields
    return [...commonFields, ...specificFields];
  }

  // Error handling for network requests
  void networkErrorHandler(Object e, String status) {
    if (e is DioException) {
      final DioException err = e;

      switch (err.type) {
        case DioExceptionType.badResponse:
          final errorData = err.response?.data as Map;
          throw Exception(errorData.values.first.first);

        case DioExceptionType.connectionTimeout:
          throw Exception('Connection Timeout. Try again');

        case DioExceptionType.receiveTimeout:
          throw Exception('Connection Timeout while loading, please try again to reload');

        case DioExceptionType.sendTimeout:
          throw Exception('Connection Timeout. Try again');

        default:
          throw Exception('Failed to $status. Please try again');
      }
    } else {
      throw Exception('Failed to $status. Please try again later');
    }
  }

  /// Download company logos from Odoo
  Future<void> downloadLogos(List<ResCompanyTableData> companies) async {
    print("Starting logo download for ${companies.length} companies");

    for(int i = 0; i < companies.length; i++){
      var company = companies[i];

      try {
        // Skip if company doesn't have a universal_id
        if (company.universal_id == null) {
          print("Skipping company ${company.name}: no universal_id");
          continue;
        }

        print("Downloading logo for company: ${company.name} (ID: ${company.universal_id})");

        // Download logo from Odoo as base64 data
        final logoData = await _downloadCompanyLogoFromOdoo(company.universal_id!);

        if (logoData != null) {
          // Save logo to local storage
          final logoFileName = await _saveLogoToLocal(logoData, company.id);

          if (logoFileName != null) {
            // Update company record with local logo filename
            await _updateCompanyLogo(company.id, logoFileName);
            print("Successfully downloaded and saved logo for company: ${company.name}");
          }
        } else {
          print("No logo data found for company: ${company.name}");
        }
      } catch (e, st) {
        print("Error downloading logo for company ${company.name}: $e");
        print("Stack trace: $st");
      }
    }
  }

  /// Download company logo from Odoo as base64 data
  Future<List<int>?> _downloadCompanyLogoFromOdoo(int companyId) async {
    try {
      // Read the logo field from Odoo (it's stored as base64)
      final result = await OdooClient.instance.executeKw(
        model: 'res.company',
        method: 'read',
        args: [[companyId]],
        kwargs: {"fields": ["logo"]}
      );

      if (result != null && result is List && result.isNotEmpty) {
        final companyData = result[0];
        final logoBase64 = companyData['logo'];

        if (logoBase64 != null && logoBase64 is String && logoBase64.isNotEmpty) {
          // Decode base64 to bytes
          return base64Decode(logoBase64);
        }
      }

      return null;
    } catch (e) {
      print("Error downloading logo from Odoo: $e");
      return null;
    }
  }

  /// Save logo bytes to local storage and return filename
  Future<String?> _saveLogoToLocal(List<int> logoBytes, int companyId) async {
    try {
      final directory = await getDownloadPath2();
      if (directory == null) {
        print("Could not get download directory");
        return null;
      }

      // Create filename with company ID
      final logoFileName = "company_${companyId}.png";
      final logoPath = "$directory$logoFileName";

      // Save file
      final file = File(logoPath);
      await file.create(recursive: true);
      await file.writeAsBytes(logoBytes);

      print("Logo saved to: $logoPath");
      return logoFileName;
    } catch (e) {
      print("Error saving logo to local storage: $e");
      return null;
    }
  }

  /// Update company record with local logo filename
  Future<void> _updateCompanyLogo(int companyId, String logoFileName) async {
    try {
      final db = DatabaseService().database;
      final company = await db.resCompanyDao.getCompanyById(companyId);

      if (company != null) {
        final companion = ResCompanyTableCompanion(
          id: Value(company.id),
          logo: Value(logoFileName),
        );
        await db.resCompanyDao.insertOrUpdateCompany(companion);
        print("Updated company ${company.name} with logo: $logoFileName");
      }
    } catch (e) {
      print("Error updating company logo: $e");
    }
  }

  /// Handle deactivated currencies by marking them as deleted in local database
  @visibleForTesting
  Future<void> handleDeactivatedCurrencies(List<Map<String, dynamic>> odooCurrencies) async {
    try {
      final db = DatabaseService().database;

      // Get all local currencies that are currently synced
      final localCurrencies = await db.resCurrencyDao.getAllCurrencies();
      final syncedLocalCurrencies = localCurrencies.where((c) => c.universal_id != null && c.is_synced).toList();

      // Create a set of active Odoo currency universal IDs for quick lookup
      final activeCurrencyIds = odooCurrencies
          .where((currency) => currency['active'] == true)
          .map((currency) => currency['id'] as int)
          .toSet();

      // Find local currencies that are no longer active in Odoo
      final currenciesToDeactivate = syncedLocalCurrencies.where((localCurrency) {
        return localCurrency.universal_id != null &&
               !activeCurrencyIds.contains(localCurrency.universal_id);
      }).toList();

      // Mark deactivated currencies as deleted in local database
      for (final currency in currenciesToDeactivate) {
        final companion = ResCurrencyTableCompanion(
          id: Value(currency.id),
          is_deleted: const Value(true),
          active: const Value(false),
        );

        await db.resCurrencyDao.insertOrUpdateCurrency(companion);
        print("Marked currency '${currency.name}' as deleted (deactivated in Odoo)");
      }

      if (currenciesToDeactivate.isNotEmpty) {
        print("Processed ${currenciesToDeactivate.length} deactivated currencies");
      }

    } catch (e) {
      print("Error handling deactivated currencies: $e");
    }
  }
}

