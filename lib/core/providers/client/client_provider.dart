import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/utils/UserPreference.dart';
import 'PaginatedResPartnerTableData.dart';
import 'ClientsRequest.dart';

// Use the Drift-based repository for clients
final clientProvider = FutureProvider.autoDispose.family<ResPartnerTableData?, int>((ref, id) {
  return ref.read(resPartnerRepositoryProvider).getById(id);
});

// Provider for creating a new client
final newClientProvider = FutureProvider.autoDispose.family<ResPartnerTableData?, int>((ref, _) async {
  // Get active business
  var prefs = await SharedPreferences.getInstance();
  var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

  if (activeBusiness == null) {
    throw Exception("Go to settings and create or select active business.");
  }

  // Create a new client with the active business
  return ResPartnerTableData(
    id:0,
    company_id: activeBusiness,
    customer_rank: 1, // Set as customer by default
    active: true, // Active by default (1 = true)
    is_synced: false,
    is_confirmed: true,
    is_deleted: false,
    version: 1,
  );
});

// Provider for paginated clients
final clientsProvider = FutureProvider.autoDispose.family<PaginatedResPartnerTableData, ClientsRequest>((ref, req) async {
  // Get active business ID
  var prefs = await SharedPreferences.getInstance();
  var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

  if (activeBusiness == null) {
    throw Exception("Go to settings and create or select active business.");
  }

  if (kIsWeb || strictWeb) {
    // Web implementation would be handled here
    throw UnimplementedError("Web implementation not available in this version");
  } else {
    // Local database implementation using Drift
    List<ResPartnerTableData> clients = [];

    if (req.query == null || req.query == "") {
      // Get all clients for the company
      clients = await ref.read(resPartnerRepositoryProvider).getForCompany(activeBusiness);

      // Filter to only include customers (customerRank > 0)
      // clients = clients.where((client) => client.customerRank != null && client.customerRank! > 0).toList();
    } else {
      // Search clients
      var searchResults = await ref.read(resPartnerRepositoryProvider).search(req.query!);

      // Filter to only include clients for the active business and customers
      clients = searchResults;
    }

    // Sort clients by name
    clients.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

    // Pagination
    int totalItems = clients.length;
    int startIndex = req.page_number * req.page_size;
    int endIndex = (startIndex + req.page_size) > clients.length
        ? clients.length
        : (startIndex + req.page_size);

    List<ResPartnerTableData> pagedClients = [];
    if (startIndex < clients.length) {
      pagedClients = clients.sublist(startIndex, endIndex);
    }

    // Create paginated result
    return PaginatedResPartnerTableData(
      content: pagedClients,
      totalItems: totalItems,
      offset: req.page_number * req.page_size,
      itemCount: pagedClients.length,
      page_number: req.page_number,
    );
  }
});
