import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/constants.dart';
import '../exceptions/exception_handler.dart';
import '../utils/UserPreference.dart';

/// Create a singleton class to handle Odoo API calls
class OdooClient {
  OdooClient._();

  static final instance = OdooClient._();

  /// Execute a JSON-RPC call to Odoo
  Future<dynamic> executeKw({
    required String model,
    required String method,
    required List args,
    Map<String, dynamic>? kwargs,
    bool ignoreAuthToken = false,
  }) async {
    try {
      // Get Odoo credentials from SharedPreferences
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final String odooUrl = prefs.getString('odooUrl') ?? '';
      final String database = prefs.getString('database') ?? '';
      final int userId = int.parse(prefs.getString(UserPreference.userId) ?? '0');
      final String password = prefs.getString('password') ?? '';

      if (odooUrl.isEmpty || database.isEmpty || userId == 0 || password.isEmpty) {
        throw Exception('Missing Odoo credentials');
      }

      // Create the JSON-RPC request
      final Map<String, dynamic> data = {
        "jsonrpc": "2.0",
        "method": "call",
        "params": {
          "service": "object",
          "method": "execute_kw",
          "args": [
            database,
            userId,
            password,
            model,
            method,
            args,
            kwargs ?? {}
          ]
        }
      };

      // Log the outgoing request (with password masked)
      final Map<String, dynamic> logData = {
        "jsonrpc": "2.0",
        "method": "call",
        "params": {
          "service": "object",
          "method": "execute_kw",
          "args": [
            database,
            userId,
            "********", // Mask password for security
            model,
            method,
            args,
            kwargs ?? {}
          ]
        }
      };
      print('ODOO REQUEST [${model}/${method}]: ${jsonEncode(logData)}');

      // Create a Dio instance for Odoo
      final Dio odooClient = Dio(
        BaseOptions(
          baseUrl: odooUrl,
          connectTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 60),
          responseType: ResponseType.json,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        )
      );

      // Make the request
      final Response response = await odooClient.post(
        odooJsonRpcEndpoint,
        data: data,
      );

      // Handle the response
      if (response.data != null) {
        // Log the response
        print('ODOO RESPONSE [${model}/${method}]: ${jsonEncode(response.data)}');

        if (response.data['error'] != null) {
          final error = response.data['error'];
          final errorMessage = error['message'] ?? 'Unknown error';
          final errorData = error['data'] ?? {};

          // Log detailed error information
          print('ODOO ERROR DETAILS:');
          print('Message: $errorMessage');

          String debugInfo = '';
          if (errorData is Map) {
            if (errorData['debug'] != null) {
              debugInfo = errorData['debug'].toString();
              print('Debug: $debugInfo');
            }

            if (errorData['exception_type'] != null) {
              print('Exception Type: ${errorData['exception_type']}');
            }

            if (errorData['arguments'] != null && errorData['arguments'] is List) {
              print('Arguments: ${errorData['arguments'].join(', ')}');
            }
          }

          // Throw a more detailed exception
          throw Exception('Odoo error: $errorMessage\nDebug: $debugInfo');
        }
        return response.data['result'] ?? [];
      }
      return null;
    } catch (e) {
      print('ERROR IN ODOO API CALL [${model}/${method}]: $e');
      if (e is DioException && e.response != null) {
        print('RESPONSE DATA: ${e.response?.data}');
      }
      throw exceptionHandler(e, 'Odoo API call');
    }
  }

  /// Create a record in Odoo
  Future<int> create(String model, Map<String, dynamic> values) async {
    print('CREATING RECORD IN ODOO [${model}]: ${jsonEncode(values)}');

    final result = await executeKw(
      model: model,
      method: 'create',
      args: [values],
    );

    if (result is int) {
      print('CREATED RECORD IN ODOO [${model}] WITH ID: ${result}');
      return result;
    }

    print('Unexpected result type from Odoo create: ${result.runtimeType}, value: $result');
    return 0;
  }

  /// Read records from Odoo
  Future<List<Map<String, dynamic>>> read(
    String model,
    List<int> ids,
    List<String> fields
  ) async {
    print('READING RECORDS FROM ODOO [${model}]');
    print('IDS: ${ids.join(", ")}');
    print('FIELDS: ${jsonEncode(fields)}');

    final result = await executeKw(
      model: model,
      method: 'read',
      args: [ids, fields],
    );

    if (result is List) {
      print('READ ${result.length} RECORDS FROM ODOO [${model}]');
      return List<Map<String, dynamic>>.from(result);
    }

    print('Unexpected result type from Odoo read: ${result.runtimeType}, value: $result');
    return [];
  }

  /// Search and read records from Odoo
  Future<List<Map<String, dynamic>>> searchRead(
    String model,
    List domain,
    List<String> fields,
    {int offset = 0, int limit = 0, String order = ''}
  ) async {
    print('SEARCHING RECORDS IN ODOO [${model}]');
    print('DOMAIN: ${jsonEncode(domain)}');
    print('FIELDS: ${jsonEncode(fields)}');

    final kwargs = <String, dynamic>{
      'fields': fields,
    };

    if (offset > 0) kwargs['offset'] = offset;
    if (limit > 0) kwargs['limit'] = limit;
    if (order.isNotEmpty) kwargs['order'] = order;

    final result = await executeKw(
      model: model,
      method: 'search_read',
      args: [domain],
      kwargs: kwargs,
    );

    if (result is List) {
      print('FOUND ${result.length} RECORDS IN ODOO [${model}]');
      return List<Map<String, dynamic>>.from(result);
    }

    print('Unexpected result type from Odoo search_read: ${result.runtimeType}, value: $result');
    return [];
  }

  /// Update records in Odoo
  Future<bool> write(String model, List<int> ids, Map<String, dynamic> values) async {
    print('UPDATING RECORDS IN ODOO [${model}] IDS: ${ids.join(", ")}');
    print('UPDATE VALUES: ${jsonEncode(values)}');

    final result = await executeKw(
      model: model,
      method: 'write',
      args: [ids, values],
    );

    if (result is bool) {
      print('UPDATED RECORDS IN ODOO [${model}] RESULT: $result');
      return result;
    }

    // Odoo often returns True as a string or 1 as an integer for success
    if (result.toString().toLowerCase() == 'true' || result == 1) {
      print('UPDATED RECORDS IN ODOO [${model}] RESULT: true (converted)');
      return true;
    }

    print('Unexpected result type from Odoo write: ${result.runtimeType}, value: $result');
    return false;
  }

  /// Delete records in Odoo
  Future<bool> unlink(String model, List<int> ids) async {
    final result = await executeKw(
      model: model,
      method: 'unlink',
      args: [ids],
    );

    if (result is bool) {
      return result;
    }

    // Odoo often returns True as a string or 1 as an integer for success
    if (result.toString().toLowerCase() == 'true' || result == 1) {
      return true;
    }

    print('Unexpected result type from Odoo unlink: ${result.runtimeType}');
    return false;
  }

  /// Get the last modified date for a model
  Future<String> getLastModifiedDate(String model) async {
    final result = await executeKw(
      model: model,
      method: 'search_read',
      args: [[]],
      kwargs: {
        'fields': ['write_date'],
        'order': 'write_date desc',
        'limit': 1,
      },
    );

    if (result is List && result.isNotEmpty) {
      return result[0]['write_date'] ?? '';
    }
    return '';
  }
}
