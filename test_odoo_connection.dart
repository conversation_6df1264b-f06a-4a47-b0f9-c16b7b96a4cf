import 'dart:convert';
import 'package:dio/dio.dart';

/// Simple script to test Odoo connection directly
void main() async {
  print('🔗 Testing Odoo Connection...');
  
  // Odoo server details
  const String odooUrl = 'https://erp.kanjan.co.zw';
  const String database = 'piggypro';
  const String username = '<EMAIL>';
  const String password = 'Secret1234';
  const int userId = 2;
  
  final dio = Dio();
  
  try {
    // Test 1: Basic authentication
    print('\n📡 Testing authentication...');
    
    final authData = {
      "jsonrpc": "2.0",
      "method": "call",
      "params": {
        "service": "common",
        "method": "authenticate",
        "args": [database, username, password, {}]
      }
    };
    
    final authResponse = await dio.post(
      '$odooUrl/jsonrpc',
      data: authData,
      options: Options(
        headers: {
          'Content-Type': 'application/json',
        },
      ),
    );
    
    print('Auth Response: ${authResponse.data}');
    
    if (authResponse.data['result'] != null) {
      final authenticatedUserId = authResponse.data['result'];
      print('✅ Authentication successful! User ID: $authenticatedUserId');
      
      // Test 2: Simple data fetch
      print('\n💱 Testing data fetch...');
      
      final fetchData = {
        "jsonrpc": "2.0",
        "method": "call",
        "params": {
          "service": "object",
          "method": "execute_kw",
          "args": [
            database,
            authenticatedUserId,
            password,
            "res.currency",
            "search_read",
            [[], ["name", "symbol"]],
            {"limit": 5}
          ]
        }
      };
      
      final fetchResponse = await dio.post(
        '$odooUrl/jsonrpc',
        data: fetchData,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );
      
      print('Fetch Response: ${fetchResponse.data}');
      
      if (fetchResponse.data['result'] != null) {
        final currencies = fetchResponse.data['result'];
        print('✅ Data fetch successful! Found ${currencies.length} currencies');
        for (var currency in currencies) {
          print('  - ${currency['name']} (${currency['symbol']})');
        }
      } else {
        print('❌ Data fetch failed: ${fetchResponse.data}');
      }
      
    } else {
      print('❌ Authentication failed: ${authResponse.data}');
    }
    
  } catch (e) {
    print('❌ Connection failed: $e');
    if (e is DioException) {
      print('Response data: ${e.response?.data}');
      print('Status code: ${e.response?.statusCode}');
    }
  }
}
