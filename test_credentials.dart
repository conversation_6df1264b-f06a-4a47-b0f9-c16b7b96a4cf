import 'package:shared_preferences/shared_preferences.dart';
import 'lib/core/utils/UserPreference.dart';

/// Simple script to test credential storage and retrieval
void main() async {
  print('🔧 Testing credential storage...');
  
  // Initialize SharedPreferences for testing
  SharedPreferences.setMockInitialValues({});
  
  // Set up credentials like the test helper does
  final prefs = await SharedPreferences.getInstance();
  await prefs.setString('odooUrl', 'https://erp.kanjan.co.zw');
  await prefs.setString('database', 'piggypro');
  await prefs.setString('username', '<EMAIL>');
  await prefs.setString('password', 'Secret1234');
  await prefs.setString('userId', '2');
  
  print('✅ Credentials set');
  
  // Retrieve credentials like OdooClient does
  final String odooUrl = prefs.getString('odooUrl') ?? '';
  final String database = prefs.getString('database') ?? '';
  final int userId = int.parse(prefs.getString(UserPreference.userId) ?? '0');
  final String password = prefs.getString('password') ?? '';
  
  print('📋 Retrieved credentials:');
  print('  URL: $odooUrl');
  print('  Database: $database');
  print('  User ID: $userId');
  print('  Password: ${password.isNotEmpty ? "***" : "EMPTY"}');
  
  if (odooUrl.isEmpty || database.isEmpty || userId == 0 || password.isEmpty) {
    print('❌ Missing credentials detected');
  } else {
    print('✅ All credentials present');
  }
}
