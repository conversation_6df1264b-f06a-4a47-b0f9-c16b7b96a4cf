Also note that currencies are note synced to odoo. we only get updates from odoo but we dont push updates to odoo for currencies. THis also applies to products and our businesses. What we only sync updates to and from odoo when it comes to invoices and clients only. Also note that simplicity is key in this code base. Lets note complicate things. We should achieve our goals with the least amount of code as possible with easy to understand code. After making these changes proceed to test the sync functionalities. Ensure syncing of invoices and clients from odoo into our local db works properly. And after we add local invoices and clients ensure they are synced to odoo properly. Remember when we sync to odoo the invoices are to be in draft state. Check also that the invoices we synced to odoo are added to odoo server. After that proceed to test sync of businesses, products and currencies. Ensure that we can add businesses, products and currencies locally and they are synced to odoo. And also ensure that we can update businesses, products and currencies in odoo and they are synced to our local db. 

I am using windows patform and my test odoo server is https://erp.kanjan.co.zw/web/login?db=piggypro

Email
<EMAIL>

Password
Secret1234