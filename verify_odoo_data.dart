import 'dart:convert';
import 'package:dio/dio.dart';

/// <PERSON>ript to verify that our test data exists in Odoo and is accessible
void main() async {
  print('🔍 Verifying Odoo Data Accessibility...');
  print('=' * 50);
  
  final verifier = OdooDataVerifier();
  
  try {
    await verifier.authenticate();
    await verifier.verifyRecentInvoices();
    await verifier.verifyRecentPartners();
    await verifier.verifyRecentProducts();
    
    print('\n' + '=' * 50);
    print('✅ Data verification completed successfully!');
    
  } catch (e) {
    print('\n' + '=' * 50);
    print('❌ Verification failed: $e');
  }
}

class OdooDataVerifier {
  static const String odooUrl = 'https://erp.kanjan.co.zw';
  static const String database = 'piggypro';
  static const String username = '<EMAIL>';
  static const String password = 'Secret1234';
  
  late final Dio dio;
  late int userId;
  
  OdooDataVerifier() {
    dio = Dio();
  }
  
  Future<void> authenticate() async {
    print('🔐 Authenticating with Odoo...');
    
    final authData = {
      "jsonrpc": "2.0",
      "method": "call",
      "params": {
        "service": "common",
        "method": "authenticate",
        "args": [database, username, password, {}]
      }
    };
    
    final response = await dio.post('$odooUrl/jsonrpc', data: authData);
    
    if (response.data['result'] != null) {
      userId = response.data['result'];
      print('✅ Authenticated as user ID: $userId');
    } else {
      throw Exception('Authentication failed: ${response.data}');
    }
  }
  
  Future<void> verifyRecentInvoices() async {
    print('\n🧾 Checking recent invoices...');
    
    // Get invoices created today
    final today = DateTime.now().toIso8601String().split('T')[0];
    
    final invoices = await _odooCall('account.move', 'search_read', [
      [
        ['move_type', '=', 'out_invoice'],
        ['create_date', '>=', '$today 00:00:00'],
      ],
      ['name', 'partner_id', 'state', 'amount_total', 'create_date']
    ]);
    
    print('Found ${invoices.length} invoices created today:');
    
    for (var invoice in invoices) {
      final partnerName = invoice['partner_id'] is List 
          ? invoice['partner_id'][1] 
          : 'Unknown Partner';
      
      print('  📄 ${invoice['name']} - $partnerName');
      print('     State: ${invoice['state']}, Amount: \$${invoice['amount_total']}');
      print('     Created: ${invoice['create_date']}');
      
      // Verify invoice is in draft state as required
      if (invoice['state'] == 'draft') {
        print('     ✅ Correctly in draft state');
      } else {
        print('     ⚠️ Not in draft state: ${invoice['state']}');
      }
    }
    
    if (invoices.isEmpty) {
      print('  ℹ️ No invoices found for today');
    }
  }
  
  Future<void> verifyRecentPartners() async {
    print('\n👥 Checking recent partners...');
    
    final today = DateTime.now().toIso8601String().split('T')[0];
    
    final partners = await _odooCall('res.partner', 'search_read', [
      [
        ['customer_rank', '>', 0],
        ['create_date', '>=', '$today 00:00:00'],
      ],
      ['name', 'email', 'phone', 'create_date']
    ]);
    
    print('Found ${partners.length} customers created today:');
    
    for (var partner in partners) {
      print('  👤 ${partner['name']}');
      print('     Email: ${partner['email'] ?? 'N/A'}');
      print('     Phone: ${partner['phone'] ?? 'N/A'}');
      print('     Created: ${partner['create_date']}');
    }
    
    if (partners.isEmpty) {
      print('  ℹ️ No new customers found for today');
    }
  }
  
  Future<void> verifyRecentProducts() async {
    print('\n📦 Checking recent products...');
    
    final today = DateTime.now().toIso8601String().split('T')[0];
    
    final products = await _odooCall('product.product', 'search_read', [
      [
        ['create_date', '>=', '$today 00:00:00'],
      ],
      ['name', 'list_price', 'default_code', 'create_date']
    ]);
    
    print('Found ${products.length} products created today:');
    
    for (var product in products) {
      print('  📦 ${product['name']}');
      print('     Price: \$${product['list_price']}');
      print('     Code: ${product['default_code'] ?? 'N/A'}');
      print('     Created: ${product['create_date']}');
    }
    
    if (products.isEmpty) {
      print('  ℹ️ No new products found for today');
    }
  }
  
  Future<dynamic> _odooCall(String model, String method, List args, [Map<String, dynamic>? kwargs]) async {
    final data = {
      "jsonrpc": "2.0",
      "method": "call",
      "params": {
        "service": "object",
        "method": "execute_kw",
        "args": [database, userId, password, model, method, args, kwargs ?? {}]
      }
    };
    
    final response = await dio.post('$odooUrl/jsonrpc', data: data);
    
    if (response.data['error'] != null) {
      throw Exception('Odoo API Error: ${response.data['error']}');
    }
    
    return response.data['result'];
  }
}
